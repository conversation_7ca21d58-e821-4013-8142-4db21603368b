<?php

namespace app\admin\service;

use think\Db;
use EasyWeChat\Factory;

class WechatService
{
    /**
     * 获取公众号实例
     *
     * @return \EasyWeChat\OfficialAccount\Application
     * @throws \Exception
     */
    public static function getOfficialAccount()
    {
        $configId = session('wx_gzh_id');
        if (empty($configId)) {
            throw new \Exception('公众号配置不存在');
        }
        $config = Db::name('org_wxgzh')->where(['id' => $configId, 'status' => 1])->find();
        if (!$config) {
            throw new \Exception('公众号配置不存在');
        }

        return Factory::officialAccount([
            'app_id' => $config['account'],
            'secret' => $config['key'],
            'oauth' => [
                'scopes' => ['snsapi_userinfo'],
                'callback' => "auth/oauthCallback",
            ],
            'response_type' => 'array',
        ]);
    }

    /**
     * 获取企业微信实例
     *
     * @return \EasyWeChat\Work\Application
     * @throws \Exception
     */
    public static function getWork()
    {
        /*
        $configId = session('wx_qy_id');
        if (empty($configId)) {
            throw new \Exception('企业微信配置不存在');
        }
        $config = Db::name('org_wxqy')->where(['id' => $configId, 'status' => 1])->find();
        if (!$config) {
            throw new \Exception('企业微信配置不存在');
        }
        */

        return Factory::work([
            'corp_id' => 'wwba99d98053628544',
            'agent_id' => '1000002',
            'secret' => 'xZQXNaJ0Zaz1Q34g7xkWErnAee-NKknpIgDimtoo2xk',
            'oauth' => [
                'callback' => 'auth/workCallback',
            ],
        ]);
    }

    /**
     * 验证公众号state参数是否有效
     *
     * @param string $state 公众号state参数
     * @return boolean
     */
    public static function validateState($state)
    {
        try {
            if (empty($state)) {
                return false;
            }
            $wx_gzh_id = getDecryptParam($state)[0];
            if (empty($wx_gzh_id)) {
                return false;
            }
            // 检查配置是否存在
            if (Db::name('org_wxgzh')->where(['id' => $wx_gzh_id, 'status' => 1])->count() > 0) {
                session('wx_gzh_id', $wx_gzh_id);
                return true;
            }
            return false;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 验证经销商/群管state参数是否有效
     *
     * @param string $state 经销商/群管state参数
     * @param int $type 1=经销商，2=群管
     * @return boolean
     */
    public static function validateStateToUser($state, $type)
    {
        try {
            if (empty($state)) {
                return false;
            }

            // 确保公众号ID是正确的
            $wx_gzh_id = getDecryptParam($state)[0];
            if (empty($wx_gzh_id)) {
                return false;
            }
            // 检查配置是否存在
            $wx_gzh_row = Db::name('org_wxgzh')->where(['id' => $wx_gzh_id, 'status' => 1])->find();
            if (!$wx_gzh_row) {
                return false;
            }
            session('wx_gzh_id', $wx_gzh_id);

            $wx_user_id = getDecryptParam($state)[1];
            if (empty($wx_user_id)) {
                return false;
            }
            // 查询经销商/群管
            $userRow = Db::name('org_wxuser')->where(['user_id' => $wx_user_id, 'status' => 9, 'type' => $type])->find();
            if (!$userRow) {
                return false;
            }

            if ($type == 1) {
                // 群管注册，获取经销商ID
                $agency_id = Db::name('org_agency')->where(['wx_user_id' => $wx_user_id])->value('agency_id');
                session('wx_dealer_id', $agency_id);
            } elseif ($type == 2) {
                // 会员注册，获取群管ID和经销商ID
                $tubeRow = Db::name('org_agency_tube')->where(['wx_user_id' => $wx_user_id])->find();
                session('wx_dealer_id', $tubeRow['agency_id']);
                session('wx_group_id', $tubeRow['tube_id']);
            }
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
}
