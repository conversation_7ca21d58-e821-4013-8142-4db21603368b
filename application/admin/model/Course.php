<?php

namespace app\admin\model;

use think\Model;
use think\Db;

class Course extends Model
{
    /**
     * 获取经销商信息
     */
    public function getAgencyInfo($openid)
    {
        $map = [
            'u.openid' => $openid,
            'a.status' => 9,
            'u.type'   => 2
        ];
        return Db::name('org_wxuser u')
            ->field('u.*,a.agency_id, a.agency_name')
            ->join('ksd_org_agency a', 'a.agency_id = u.agency_id', 'left')
            // ->join('ksd_org_agency_balance b', 'b.agency_id = a.agency_id', 'left')
            ->where($map)
            ->find();
    }
    /**
     * 获取当日营期item
     */
    public function getPeriodItem($agencyId)
    {
        $time = time();
        $where = [];
        $where[] = ['p.status', '=', 1];
        $where[] = ['p.del', '=', 0];
        $where[] = ['g.status', '=', 1];
        $where[] = ['g.del', '=', 0];
        $where[] = ['i.del', '=', 0];
        $where[] = ['i.status', '=', 1];
        $where[] = ['i.start_time', '<=', $time];
        $where[] = ['i.end_time', '>=', $time];
        return  Db::name('org_period_item i')
                ->leftJoin('org_period p', 'i.period_id = p.period_id')
                ->leftjoin('org_period_group g', 'g.group_id = p.group_id')
                ->leftJoin('org_course c', 'c.course_id = i.course_id')
                ->leftJoin('org_video v', 'v.video_id = c.video_id')
                ->field('g.group_name,p.period_name,i.item_name,i.start_time,i.end_time,c.cover_url,v.oss_video_id,v.duration,i.item_id')
                ->where($where)
                ->whereRaw("FIND_IN_SET(?, p.agency_ids)", [$agencyId])
                ->select();
    }

    /**
     * 获取课程信息
     *
     * @param [type] $course_id
     * @return void
     */
    public function getCourseInfo($item_id)
    {
        $question_item = [];
        $map = [
            'i.item_id' => $item_id,
            'c.del' => 0,
            'c.status' => 1,
        ];
        $data = Db::name('org_period_item i')
            ->leftJoin('org_course c', 'c.course_id = i.course_id')
            ->leftJoin('org_video v', 'v.video_id = i.video_id')
            ->leftJoin('org_question q', 'q.question_id = i.question_id')
            ->field('i.*,c.course_name,c.desc,c.finish_play,v.oss_video_id,v.duration,q.question_name')
            ->where($map)
            ->find();
        if (!empty($data)) {
            $question_item = Db::name('org_question_item')->where(['question_id' => $data['question_id']])->select();
        }
        $data['question_item'] = $question_item;
        return $data;
    }

    /**
     * 保存用户答题结果
     *
     * @param int $user_id 用户ID
     * @param int $item_id 课程项目ID
     * @param int $answer_id 答案ID
     * @return array 处理结果
     */
    public function saveUserAnswer($user_id, $item_id, $answer_id)
    {
        // 参数验证
        if (empty($user_id) || empty($item_id) || empty($answer_id)) {
            return ['code' => 400, 'msg' => '参数错误'];
        }

        // 获取课程信息
        $courseInfo = $this->getCourseInfo($item_id);
        if (empty($courseInfo)) {
            return ['code' => 400, 'msg' => '课程信息不存在'];
        }

        // 查询是否已经答题 - 每个会员只有一次答题机会
        $existAnswer = Db::name('org_user_watch')
            ->where([
                'user_id' => $user_id,
                'item_id' => $item_id,
                'question_id' => $courseInfo['question_id']
            ])
            ->find();
        
        // 判断是否已完播
        if($existAnswer['is_finish'] == 0){
            return ['code' => 400, 'msg' => '请先看完视频在进行答题'];
        }
        // 判断是否已答题
        if ($existAnswer['answer_id'] > 0) {
            // 已经答过题，不允许再次答题
            return [
                'code' => 400,
                'msg' => '您已经答过题了，每个会员只有一次答题机会',
                'data' => [
                    'is_correct' => $existAnswer['is_correct'],
                    'answer_id' => $existAnswer['answer_id'],
                    'already_answered' => true
                ]
            ];
        }

        // 查询选择的答案是否正确
        $answerItem = Db::name('org_question_item')
            ->where([
                'item_id' => $answer_id,
                'question_id' => $courseInfo['question_id']
            ])
            ->find();

        if (empty($answerItem)) {
            return ['code' => 400, 'msg' => '答案选项不存在'];
        }

        $isCorrect = $answerItem['is_true'] == 1;

        // 更新答题记录
        $updateData = [
            'answer_id' => $answer_id,
            'is_correct' => $isCorrect ? 1 : 0,
            'update_time' => time()
        ];

        $result = Db::name('org_user_watch')->where('id',$existAnswer['id'])->update($updateData);

        if (!$result) {
            return ['code' => 500, 'msg' => '保存答题记录失败'];
        }

        // 获取正确答案信息
        $correctAnswer = Db::name('org_question_item')
            ->where([
                'question_id' => $courseInfo['question_id'],
                'is_true' => 1
            ])
            ->find();

        return [
            'code' => 200,
            'msg' => '提交成功',
            'data' => [
                'is_correct' => $isCorrect,
                'answer_id' => $answer_id,
                'correct_item_id' => $correctAnswer ? $correctAnswer['item_id'] : 0,
                'correct_option_value' => $correctAnswer ? $correctAnswer['option_value'] : '',
                'already_answered' => false
            ]
        ];
    }

    /**
     * 获取用户答题记录
     *
     * @param int $user_id 用户ID
     * @param int $item_id 课程项目ID
     * @return array|null 用户答题记录
     */
    public function getUserAnswer($user_id, $item_id,$question_id)
    {
        if (empty($user_id) || empty($item_id) || empty($question_id)) {
            return null;
        }

        $map = [
            ['a.user_id','=',$user_id],
            ['a.item_id','=',$item_id],
            ['a.question_id','=',$question_id],
            ['a.answer_id','neq',0]
        ];

        // 查询用户答题记录
        $userAnswer = Db::name('org_user_watch')
            ->alias('a')
            ->leftJoin('org_question_item i', 'a.answer_id = i.item_id')
            ->field('a.*, i.option_name, i.option_value, i.is_true')
            ->where($map)
            ->find();

        if ($userAnswer) {
            // 查询正确答案
            if ($userAnswer['is_correct'] == 0) {
                $correctAnswer = Db::name('org_question_item')
                    ->where([
                        'question_id' => $userAnswer['question_id'],
                        'is_true' => 1
                    ])
                    ->find();

                if ($correctAnswer) {
                    $userAnswer['correct_item_id'] = $correctAnswer['item_id'];
                    $userAnswer['correct_option_value'] = $correctAnswer['option_value'];
                }
            }
        }

        return $userAnswer;
    }

    /**
     * 更新用户播放记录
     *
     * @param int $user_id 用户ID
     * @param int $item_id 课程项目ID
     * * @param int $finished 完播
     * @return bool 处理结果
     */
    public function updateUserWatchRecord($user_id, $item_id, $finished)
    {
        // 获取课程信息
        $courseInfo = $this->getCourseInfo($item_id);
        if (empty($courseInfo)) {
            return false;
        }
        // 查询是否已有学习记录
        $existRecord = Db::name('org_user_watch')
            ->where([
                'user_id' => $user_id,
                'item_id' => $item_id,
            ])
            ->find();
        if(!empty($existRecord)){
            if($finished == 1){
                $updateData = [
                    'is_finish' => 1,
                    'end_play_time' => time(),
                    'update_time' => time()
                ];
                $map = [
                    ['id','=',$existRecord['id']],
                    ['is_finish','=',0]
                ];
                return Db::name('org_user_watch')
                    ->where($map)
                    ->update($updateData) !== false;
            }
            return true;
        }else{
            $userInfo = Db::name('org_wxuser')->where(['user_id' => $user_id])->find();
            $insertData = [
                'org_id' => $userInfo['org_id'],
                'agency_id' => $userInfo['agency_id'],
                'tube_id' => $userInfo['tube_id'],
                'user_id' => $user_id,
                'item_id' => $item_id,
                'video_id' => $courseInfo['video_id'],
                'question_id' => $courseInfo['question_id'],
                'is_finish' => 0,
                'first_play_time' => time(),
                'create_time' => time()
            ];
            return Db::name('org_user_watch')->insert($insertData) !== false;
        }
    }
}
