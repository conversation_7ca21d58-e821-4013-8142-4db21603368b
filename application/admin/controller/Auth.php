<?php

namespace app\admin\controller;

use think\Controller;
use think\Db;
use think\facade\Request;
use app\admin\service\WechatService;

class Auth extends Controller
{
    protected $openid;
    protected function initialize()
    {
        parent::initialize();

        // 根据授权类型选择不同的session key
        if (!empty(session('wx_qy_id'))) {
            $sessionKey = 'work_user_' . session('wx_qy_id');
        } else {
            $sessionKey = 'wechat_user_' . session('wx_gzh_id');
        }

        $this->openid = session("$sessionKey.id");
    }

    /**
     * 经销商注册微信授权
     *
     * @return void
     */
    public function dealer()
    {
        // 接收公众号ID
        $state = Request::param('state');
        //验证state是否正确
        if (empty($state)) {
            return redirect('error/index')->with('error_msg', '无效的授权请求-101');
        }

        // 判断是否为企业微信授权并验证state参数
        if (WechatService::isWorkState($state)) {
            if (!WechatService::validateWorkState($state)) {
                return redirect('error/index')->with('error_msg', '无效的企业微信授权请求-102');
            }
        } else {
            if (!WechatService::validateState($state)) {
                return redirect('error/index')->with('error_msg', '无效的授权请求-102');
            }
        }
        if (!empty($this->openid)) {
            // 验证当前登录用户类型，是否是经销商，如果不是，直接阻止
            if (!getUserType($this->openid)) {
                return redirect('error/index')->with('error_msg', '无效的授权请求-103');
            }
            if (getUserType($this->openid) == 2) {
                return redirect('error/index')->with('error_msg', '你当前已注册群管，请勿重复注册');
            }
            if (getUserType($this->openid) == 3) {
                return redirect('error/index')->with('error_msg', '你当前已注册会员，请勿重复注册');
            }
            return redirect('register/dealer');
        }
        return $this->fetch();
    }

    /**
     * 群管注册微信授权
     *
     * @return void
     */
    public function groupMgr()
    {
        // 接收经销商ID
        $state = Request::param('state');
        //验证state是否正确
        if (empty($state)) {
            return redirect('error/index')->with('error_msg', '无效的授权请求-104');
        }

        // 判断是否为企业微信授权并验证state参数
        if (WechatService::isWorkState($state)) {
            if (!WechatService::validateWorkStateToUser($state, 1)) {
                return redirect('error/index')->with('error_msg', '无效的企业微信授权请求-105');
            }
        } else {
            if (!WechatService::validateStateToUser($state, 1)) {
                return redirect('error/index')->with('error_msg', '无效的授权请求-105');
            }
        }
        if (!empty($this->openid)) {
            // 验证当前登录用户类型，是否是群管，如果不是，直接阻止
            if (!getUserType($this->openid)) {
                return redirect('error/index')->with('error_msg', '无效的授权请求-106');
            }
            if (getUserType($this->openid) == 1) {
                return redirect('error/index')->with('error_msg', '你当前已注册经销商，请勿重复注册');
            }
            if (getUserType($this->openid) == 3) {
                return redirect('error/index')->with('error_msg', '你当前已注册会员，请勿重复注册');
            }
            return redirect('register/groupMgr');
        }
        return $this->fetch("group");
    }

    /**
     * 会员注册微信授权
     *
     * @return void
     */
    public function member()
    {
        // 接收群管ID
        $state = Request::param('state');
        //验证state是否正确
        if (empty($state)) {
            return redirect('error/index')->with('error_msg', '无效的授权请求-107');
        }

        // 判断是否为企业微信授权并验证state参数
        if (WechatService::isWorkState($state)) {
            if (!WechatService::validateWorkStateToUser($state, 2)) {
                return redirect('error/index')->with('error_msg', '无效的企业微信授权请求-108');
            }
        } else {
            if (!WechatService::validateStateToUser($state, 2)) {
                return redirect('error/index')->with('error_msg', '无效的授权请求-108');
            }
        }
        if (!empty($this->openid)) {
            // 验证当前登录用户类型，是否是会员，如果不是，直接阻止
            if (!getUserType($this->openid)) {
                return redirect('error/index')->with('error_msg', '无效的授权请求-109');
            }
            if (getUserType($this->openid) == 1) {
                return redirect('error/index')->with('error_msg', '你当前已注册经销商，请勿重复注册');
            }
            if (getUserType($this->openid) == 2) {
                return redirect('error/index')->with('error_msg', '你当前已注册群管，请勿重复注册');
            }
        }
        return redirect('register/member');
    }

    // 公众号授权回调
    public function oauthCallback()
    {
        try {
            $app = WechatService::getOfficialAccount();
            $user = $app->oauth->user();

            // 保存用户信息
            $sessionKey = 'wechat_user_' . session('wx_gzh_id');
            session($sessionKey, $user->toArray());

            $target_url = session('target_url');
            return redirect($target_url);
        } catch (\Exception $e) {
            return redirect('error/index')->with('error_msg', '微信授权失败，请重试');
        }
    }

    // 企业微信授权回调
    public function workCallback()
    {
        try {
            $app = WechatService::getWork();
            $user = $app->oauth->user();

            // 保存用户信息到企业微信专用session
            $sessionKey = 'work_user_' . session('wx_qy_id');
            session($sessionKey, $user->toArray());

            // 获取跳转目标URL
            $targetUrl = session('target_url');
            if (empty($targetUrl)) {
                $targetUrl = 'index/index'; // 默认跳转到首页
            }

            // 跳转回原页面
            return redirect($targetUrl);
        } catch (\Exception $e) {
            return redirect('error/index')->with('error_msg', '企业微信授权失败，请重试');
        }
    }
}
